/**
 * Dhan Market Data API Route
 * Server-side endpoint to fetch live market data securely
 */

import { NextRequest, NextResponse } from 'next/server';
import { fetchDhanMarketData } from '@/lib/dhan-api';

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Market Data API: Starting request...');
    const response = await fetchDhanMarketData();

    console.log('🔍 Market Data API: Response received:', {
      success: response.success,
      dataLength: response.data?.length || 0,
      error: response.error
    });

    if (!response.success) {
      console.error('❌ Market Data API: Request failed:', response.error);
      return NextResponse.json(
        {
          error: response.error?.errorMessage || 'Failed to fetch market data',
          details: response.error
        },
        { status: response.error?.errorCode === 'MISSING_CREDENTIALS' ? 500 : 400 }
      );
    }

    console.log('✅ Market Data API: Success, returning data');
    return NextResponse.json({
      success: true,
      data: response.data,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('💥 Error in Dhan market data API route:', error);

    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Handle CORS if needed
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
