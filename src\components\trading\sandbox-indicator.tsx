/**
 * Sandbox Mode Indicator
 * Shows when the system is running in sandbox mode with clear visual indicators
 */

'use client';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { 
  TestTube, 
  Shield, 
  AlertTriangle, 
  CheckCircle,
  Info
} from 'lucide-react';

interface SandboxIndicatorProps {
  isActive: boolean;
  tradingMode: 'SANDBOX' | 'LIVE';
  className?: string;
}

export function SandboxIndicator({ 
  isActive, 
  tradingMode, 
  className = '' 
}: SandboxIndicatorProps) {
  if (tradingMode === 'LIVE') {
    return (
      <Card className={`border-red-800 bg-red-900/20 ${className}`}>
        <CardContent className="p-4">
          <div className="flex items-center space-x-3">
            <div className="flex items-center justify-center w-10 h-10 bg-red-900/50 rounded-full">
              <AlertTriangle className="h-5 w-5 text-red-400" />
            </div>
            <div className="flex-1">
              <div className="flex items-center space-x-2">
                <Badge variant="destructive" className="text-xs font-semibold">
                  LIVE TRADING
                </Badge>
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-red-400 rounded-full animate-pulse"></div>
                  <span className="text-xs text-red-300">Real Money</span>
                </div>
              </div>
              <p className="text-xs text-red-300 mt-1">
                All trades will be executed with real money. Exercise caution.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`border-blue-800 bg-blue-900/20 ${className}`}>
      <CardContent className="p-4">
        <div className="flex items-center space-x-3">
          <div className="flex items-center justify-center w-10 h-10 bg-blue-900/50 rounded-full">
            <TestTube className="h-5 w-5 text-blue-400" />
          </div>
          <div className="flex-1">
            <div className="flex items-center space-x-2">
              <Badge variant="secondary" className="text-xs font-semibold bg-blue-800 text-blue-100">
                SANDBOX MODE
              </Badge>
              <div className="flex items-center space-x-1">
                <Shield className="h-3 w-3 text-blue-400" />
                <span className="text-xs text-blue-300">Safe Testing</span>
              </div>
            </div>
            <p className="text-xs text-blue-300 mt-1">
              All trades are simulated. No real money is involved.
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

interface SandboxStatsProps {
  stats: {
    totalOrders: number;
    successfulOrders: number;
    rejectedOrders: number;
    successRate: number;
    averageLatency: number;
    totalVolume: number;
  };
  className?: string;
}

export function SandboxStats({ stats, className = '' }: SandboxStatsProps) {
  return (
    <Card className={`border-gray-700 bg-gray-800/50 ${className}`}>
      <CardContent className="p-4">
        <div className="flex items-center space-x-2 mb-3">
          <TestTube className="h-4 w-4 text-blue-400" />
          <h3 className="text-sm font-medium text-white">Sandbox Statistics</h3>
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="flex justify-between text-xs">
              <span className="text-gray-400">Total Orders:</span>
              <span className="text-white font-medium">{stats.totalOrders}</span>
            </div>
            <div className="flex justify-between text-xs">
              <span className="text-gray-400">Successful:</span>
              <span className="text-green-400 font-medium">{stats.successfulOrders}</span>
            </div>
            <div className="flex justify-between text-xs">
              <span className="text-gray-400">Rejected:</span>
              <span className="text-red-400 font-medium">{stats.rejectedOrders}</span>
            </div>
          </div>
          
          <div className="space-y-2">
            <div className="flex justify-between text-xs">
              <span className="text-gray-400">Success Rate:</span>
              <span className="text-white font-medium">
                {(stats.successRate * 100).toFixed(1)}%
              </span>
            </div>
            <div className="flex justify-between text-xs">
              <span className="text-gray-400">Avg Latency:</span>
              <span className="text-white font-medium">
                {stats.averageLatency.toFixed(0)}ms
              </span>
            </div>
            <div className="flex justify-between text-xs">
              <span className="text-gray-400">Volume:</span>
              <span className="text-white font-medium">
                ₹{stats.totalVolume.toFixed(2)}
              </span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

interface SandboxBannerProps {
  className?: string;
}

export function SandboxBanner({ className = '' }: SandboxBannerProps) {
  return (
    <div className={`bg-blue-900/30 border border-blue-800 rounded-lg p-3 ${className}`}>
      <div className="flex items-center space-x-3">
        <Info className="h-5 w-5 text-blue-400 flex-shrink-0" />
        <div className="flex-1">
          <p className="text-sm text-blue-200">
            <span className="font-semibold">Sandbox Mode Active:</span> All trading operations are simulated. 
            No real money is involved. Switch to Live mode when ready for actual trading.
          </p>
        </div>
        <Badge variant="secondary" className="bg-blue-800 text-blue-100 text-xs">
          SAFE
        </Badge>
      </div>
    </div>
  );
}

interface LiveTradingWarningProps {
  className?: string;
}

export function LiveTradingWarning({ className = '' }: LiveTradingWarningProps) {
  return (
    <div className={`bg-red-900/30 border border-red-800 rounded-lg p-3 ${className}`}>
      <div className="flex items-center space-x-3">
        <AlertTriangle className="h-5 w-5 text-red-400 flex-shrink-0" />
        <div className="flex-1">
          <p className="text-sm text-red-200">
            <span className="font-semibold">Live Trading Active:</span> All orders will be executed with real money. 
            Ensure your strategy is thoroughly tested before enabling auto-trade.
          </p>
        </div>
        <Badge variant="destructive" className="text-xs">
          LIVE
        </Badge>
      </div>
    </div>
  );
}
