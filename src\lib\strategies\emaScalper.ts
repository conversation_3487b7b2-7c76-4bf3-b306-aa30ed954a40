// strategy/emaScalper.ts

export interface Candle {
  close: number;
}

export interface Signal {
  type: "BUY" | "SELL" | null;
  price: number;
  index: number; // candle index
}

export class EmaScalper {
  private length: number;
  private emaValues: number[] = [];

  constructor(length = 20) {
    this.length = length;
  }

  // Calculate EMA for a series
  private calcEMA(values: number[]): number[] {
    const k = 2 / (this.length + 1);
    const ema: number[] = [];
    let prevEma = values[0];
    ema.push(prevEma);

    for (let i = 1; i < values.length; i++) {
      const curEma = values[i] * k + prevEma * (1 - k);
      ema.push(curEma);
      prevEma = curEma;
    }
    return ema;
  }

  // Highest value in last N candles
  private highest(values: number[], period: number): number {
    return Math.max(...values.slice(-period));
  }

  // Lowest value in last N candles
  private lowest(values: number[], period: number): number {
    return Math.min(...values.slice(-period));
  }

  // Run strategy on candle closes
  public runStrategy(candles: Candle[]): Signal[] {
    const closes = candles.map(c => c.close);
    this.emaValues = this.calcEMA(closes);

    const signals: Signal[] = [];

    for (let i = 1; i < closes.length; i++) {
      const prevClose = closes[i - 1];
      const prevEma = this.emaValues[i - 1];
      const curClose = closes[i];
      const curEma = this.emaValues[i];

      const last8h = this.highest(closes.slice(0, i + 1), 8);
      const last8l = this.lowest(closes.slice(0, i + 1), 8);

      let signal: Signal = { type: null, price: curClose, index: i };

      // Bearish: cross down
      if (prevClose > prevEma && curClose < curEma) {
        signal.type = "SELL";
      }
      // Bullish: cross up
      else if (prevClose < prevEma && curClose > curEma) {
        signal.type = "BUY";
      }

      if (signal.type) {
        signals.push(signal);
        console.log(
          `${signal.type} at ${curClose} | last8H: ${last8h}, last8L: ${last8l}`
        );
      }
    }

    return signals;
  }
}
