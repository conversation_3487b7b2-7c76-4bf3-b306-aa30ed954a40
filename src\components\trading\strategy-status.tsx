/**
 * Strategy Status Component
 * Displays real-time strategy performance and status
 */

'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Activity, 
  TrendingUp, 
  TrendingDown, 
  Target, 
  AlertCircle,
  CheckCircle,
  Clock,
  BarChart3
} from 'lucide-react';
import { TradingConfiguration, TradingStats } from '@/lib/trading-config';

interface StrategyStatusProps {
  config: TradingConfiguration | null;
  stats: TradingStats | null;
  isActive: boolean;
  recentSignalsCount: number;
  recentTradesCount: number;
  loading?: boolean;
}

export function StrategyStatus({
  config,
  stats,
  isActive,
  recentSignalsCount,
  recentTradesCount,
  loading = false
}: StrategyStatusProps) {
  if (loading || !config || !stats) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Activity className="h-5 w-5" />
            <span>Strategy Status</span>
          </CardTitle>
          <CardDescription>Loading strategy performance...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            <div className="h-6 bg-gray-700 rounded"></div>
            <div className="h-4 bg-gray-700 rounded"></div>
            <div className="h-8 bg-gray-700 rounded"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const tradeProgressPercentage = (stats.dailyTradeCount / stats.maxTradesPerDay) * 100;
  const winRate = stats.dailyTradeCount > 0 ? 
    Math.round((recentTradesCount / stats.dailyTradeCount) * 100) : 0;

  const getStatusColor = () => {
    if (!isActive) return 'text-gray-400';
    if (!config.autoTradeEnabled) return 'text-yellow-400';
    if (stats.canTrade) return 'text-green-400';
    return 'text-red-400';
  };

  const getStatusText = () => {
    if (!isActive) return 'Inactive';
    if (!config.autoTradeEnabled) return 'Manual Mode';
    if (stats.canTrade) return 'Active & Trading';
    return 'Limit Reached';
  };

  const getStatusIcon = () => {
    if (!isActive) return <AlertCircle className="h-4 w-4" />;
    if (!config.autoTradeEnabled) return <Clock className="h-4 w-4" />;
    if (stats.canTrade) return <CheckCircle className="h-4 w-4" />;
    return <AlertCircle className="h-4 w-4" />;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Activity className="h-5 w-5" />
            <span>Strategy Status</span>
          </div>
          <div className={`flex items-center space-x-2 ${getStatusColor()}`}>
            {getStatusIcon()}
            <span className="text-sm font-medium">{getStatusText()}</span>
          </div>
        </CardTitle>
        <CardDescription>
          Real-time performance of the EMA Scalper strategy
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Strategy Configuration */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-1">
            <div className="text-sm text-gray-400">Strategy</div>
            <div className="font-medium">{config.strategyName}</div>
          </div>
          <div className="space-y-1">
            <div className="text-sm text-gray-400">EMA Length</div>
            <div className="font-medium">{config.strategyParams.emaLength}</div>
          </div>
          <div className="space-y-1">
            <div className="text-sm text-gray-400">Lookback</div>
            <div className="font-medium">{config.strategyParams.lookbackPeriod}</div>
          </div>
          <div className="space-y-1">
            <div className="text-sm text-gray-400">Mode</div>
            <Badge 
              variant={config.tradingMode === 'LIVE' ? 'destructive' : 'secondary'}
              className="text-xs"
            >
              {config.tradingMode}
            </Badge>
          </div>
        </div>

        {/* Daily Progress */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Daily Trade Limit</span>
            <span className="text-sm text-gray-400">
              {stats.dailyTradeCount} / {stats.maxTradesPerDay}
            </span>
          </div>
          <Progress 
            value={tradeProgressPercentage} 
            className="h-2"
          />
          <div className="flex items-center justify-between text-xs text-gray-400">
            <span>{stats.remainingTrades} trades remaining</span>
            <span>{tradeProgressPercentage.toFixed(1)}% used</span>
          </div>
        </div>

        {/* Performance Metrics */}
        <div className="grid grid-cols-3 gap-4">
          <div className="text-center p-3 bg-gray-800/50 rounded-lg">
            <div className="flex items-center justify-center mb-1">
              <BarChart3 className="h-4 w-4 text-blue-400" />
            </div>
            <div className="text-lg font-semibold text-white">
              {recentSignalsCount}
            </div>
            <div className="text-xs text-gray-400">Signals Today</div>
          </div>
          
          <div className="text-center p-3 bg-gray-800/50 rounded-lg">
            <div className="flex items-center justify-center mb-1">
              <Target className="h-4 w-4 text-green-400" />
            </div>
            <div className="text-lg font-semibold text-white">
              {stats.dailyTradeCount}
            </div>
            <div className="text-xs text-gray-400">Trades Today</div>
          </div>
          
          <div className="text-center p-3 bg-gray-800/50 rounded-lg">
            <div className="flex items-center justify-center mb-1">
              {stats.dailyPnl >= 0 ? (
                <TrendingUp className="h-4 w-4 text-green-400" />
              ) : (
                <TrendingDown className="h-4 w-4 text-red-400" />
              )}
            </div>
            <div className={`text-lg font-semibold ${stats.dailyPnl >= 0 ? 'text-green-400' : 'text-red-400'}`}>
              ₹{Math.abs(stats.dailyPnl).toFixed(2)}
            </div>
            <div className="text-xs text-gray-400">Daily P&L</div>
          </div>
        </div>

        {/* Risk Management */}
        <div className="space-y-3 pt-4 border-t border-gray-700">
          <div className="text-sm font-medium">Risk Management</div>
          <div className="grid grid-cols-2 gap-4 text-xs">
            <div>
              <span className="text-gray-400">Max Loss/Trade:</span>
              <div className="font-medium">₹{config.strategyParams.riskManagement.maxLossPerTrade}</div>
            </div>
            <div>
              <span className="text-gray-400">Max Daily Loss:</span>
              <div className="font-medium">₹{config.strategyParams.riskManagement.maxDailyLoss}</div>
            </div>
            <div>
              <span className="text-gray-400">Stop Loss:</span>
              <div className="font-medium">{config.strategyParams.riskManagement.stopLossPercentage}%</div>
            </div>
            <div>
              <span className="text-gray-400">Take Profit:</span>
              <div className="font-medium">{config.strategyParams.riskManagement.takeProfitPercentage}%</div>
            </div>
          </div>
        </div>

        {/* Subscribed Instruments */}
        <div className="space-y-3 pt-4 border-t border-gray-700">
          <div className="text-sm font-medium">Active Instruments</div>
          <div className="flex flex-wrap gap-2">
            {config.subscribedInstruments
              .filter(inst => inst.enabled)
              .map((instrument) => (
                <Badge key={instrument.symbol} variant="outline" className="text-xs">
                  {instrument.symbol}
                </Badge>
              ))}
          </div>
        </div>

        {/* Status Messages */}
        {!stats.canTrade && config.autoTradeEnabled && (
          <div className="flex items-center space-x-2 p-3 bg-yellow-900/20 border border-yellow-800 rounded text-xs text-yellow-300">
            <AlertCircle className="h-4 w-4" />
            <span>Daily trade limit reached. Trading will resume tomorrow.</span>
          </div>
        )}

        {!config.autoTradeEnabled && (
          <div className="flex items-center space-x-2 p-3 bg-blue-900/20 border border-blue-800 rounded text-xs text-blue-300">
            <Clock className="h-4 w-4" />
            <span>Auto-trade is disabled. Signals will be generated but not executed.</span>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
