"use client"

import { useAuth } from '@/contexts/auth-context'
import { But<PERSON> } from '@/components/ui/button'
import { Loading } from '@/components/ui/loading'
import { LogOut, User, TrendingUp } from 'lucide-react'
import { useDhanProfile } from '@/hooks/use-dhan-profile'
import { useMarketDataWithControl } from '@/hooks/use-market-data'
import { ProfileCard } from '@/components/dashboard/profile-card'
import { ProfileError } from '@/components/dashboard/profile-error'
import { ProfileLoading } from '@/components/dashboard/profile-loading'
import { DemoProfileData } from '@/components/dashboard/demo-profile-data'
import { MarketDataCard } from '@/components/dashboard/market-data-card'
import { MarketDataError } from '@/components/dashboard/market-data-error'
import { MarketDataLoading } from '@/components/dashboard/market-data-loading'
import { DemoMarketData } from '@/components/dashboard/demo-market-data'
import { MarketRefreshControl } from '@/components/dashboard/market-refresh-control'
import { MarketStatusIndicator } from '@/components/dashboard/market-status-indicator'
import { ErrorBoundary } from '@/components/error-boundary'

export default function Dashboard() {
  const { user, signOut, loading } = useAuth()
  const { data: profileData, loading: profileLoading, error: profileError, refetch } = useDhanProfile()
  const {
    data: marketData,
    loading: marketLoading,
    error: marketError,
    lastUpdated: marketLastUpdated,
    refetch: refetchMarket,
    isAutoRefreshEnabled,
    toggleAutoRefresh,
    status: marketStatus
  } = useMarketDataWithControl(120000) // 2 minutes to reduce API calls

  const handleSignOut = async () => {
    try {
      await signOut()
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <Loading size="lg" />
      </div>
    )
  }

  // If no user is authenticated, redirect to sign in
  if (!user) {
    window.location.href = '/'
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <Loading size="lg" />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="sticky top-0 z-50 border-b border-gray-800 bg-background/95 backdrop-blur">
        <div className="mx-auto flex h-16 max-w-8xl items-center justify-between px-4 sm:px-6 lg:px-8">
          <div className="flex items-center space-x-4">
            <div className="h-8 w-8 rounded bg-green-600 flex items-center justify-center">
              <span className="text-white font-bold text-sm">T</span>
            </div>
            <span className="font-bold text-white">Trade Dashboard</span>
          </div>

          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2 text-gray-300">
              <User className="h-4 w-4" />
              <span className="text-sm">{user?.email}</span>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={handleSignOut}
              className="border-gray-600 text-gray-300 hover:bg-gray-800"
            >
              <LogOut className="h-4 w-4 mr-2" />
              Sign Out
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="min-h-[calc(100vh-4rem)] px-4 py-8">
        <div className="max-w-6xl mx-auto space-y-8">
          {/* Welcome Section */}
          <div className="text-center space-y-4">
            <h1 className="text-4xl font-bold text-white">Trading Dashboard</h1>
            <p className="text-xl text-gray-400">Welcome back, {user?.email}</p>
          </div>

          {/* Profile Section */}
          <ErrorBoundary>
            <div className="space-y-6">
              <div className="flex items-center space-x-3">
                <TrendingUp className="h-6 w-6 text-blue-400" />
                <h2 className="text-2xl font-semibold text-white">Dhan Profile</h2>
              </div>

              {profileLoading && <ProfileLoading />}

              {profileError && (
                // Show demo data if it's an authentication error (expired token)
                profileError.toLowerCase().includes('invalid') ||
                profileError.toLowerCase().includes('expired') ||
                profileError.toLowerCase().includes('authentication') ? (
                  <DemoProfileData />
                ) : (
                  <ProfileError
                    error={profileError}
                    onRetry={refetch}
                    isRetrying={profileLoading}
                  />
                )
              )}

              {profileData && <ProfileCard profile={profileData} />}
            </div>
          </ErrorBoundary>

          {/* Market Data Section */}
          <ErrorBoundary>
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <TrendingUp className="h-6 w-6 text-green-400" />
                  <h2 className="text-2xl font-semibold text-white">Live Market Data</h2>
                </div>
                <div className="relative">
                  <MarketRefreshControl
                    isAutoRefreshEnabled={isAutoRefreshEnabled}
                    onToggleAutoRefresh={toggleAutoRefresh}
                    onManualRefresh={refetchMarket}
                    isRefreshing={marketLoading}
                    lastUpdated={marketLastUpdated}
                    refreshInterval={120000}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
                <div className="lg:col-span-1">
                  <MarketStatusIndicator status={marketStatus} />
                </div>
                <div className="lg:col-span-3">
                  {marketLoading && <MarketDataLoading />}

                  {!marketLoading && marketError && marketStatus.dataSource === 'DEMO' && (
                    <MarketDataError
                      error={marketError}
                      onRetry={refetchMarket}
                      isRetrying={marketLoading}
                    />
                  )}

                  {!marketLoading && marketData && marketData.length > 0 && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {marketData.map((data) => (
                        <MarketDataCard
                          key={data.symbol}
                          data={data}
                          dataSource={marketStatus.dataSource}
                          isLive={marketStatus.isLive}
                        />
                      ))}
                    </div>
                  )}

                  {!marketLoading && (!marketData || marketData.length === 0) && marketStatus.dataSource === 'DEMO' && (
                    <DemoMarketData />
                  )}
                </div>
              </div>
            </div>
          </ErrorBoundary>

          {/* Authentication Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-gray-800/50 border border-gray-700 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
                <User className="h-5 w-5" />
                <span>Authentication Info</span>
              </h3>
              <div className="space-y-2">
                <p className="text-gray-400 text-sm">Email: <span className="text-white">{user?.email}</span></p>
                <p className="text-gray-400 text-sm">User ID: <span className="text-white font-mono">{user?.id}</span></p>
                <p className="text-gray-400 text-sm">
                  Created: <span className="text-white">{user?.created_at ? new Date(user.created_at).toLocaleDateString() : 'N/A'}</span>
                </p>
              </div>
            </div>

            <div className="bg-gray-800/50 border border-gray-700 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-white mb-4">Session Status</h3>
              <div className="space-y-2">
                <p className="text-green-400 text-sm flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                  <span>Authenticated</span>
                </p>
                <p className="text-gray-400 text-sm">Provider: <span className="text-white">{user?.app_metadata?.provider || 'email'}</span></p>
                <p className="text-gray-400 text-sm">
                  Last Sign In: <span className="text-white">{user?.last_sign_in_at ? new Date(user.last_sign_in_at).toLocaleString() : 'N/A'}</span>
                </p>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
