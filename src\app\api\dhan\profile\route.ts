/**
 * Dhan Profile API Route
 * Server-side endpoint to fetch Dhan profile data securely
 */

import { NextRequest, NextResponse } from 'next/server';
import { fetchDhanProfile } from '@/lib/dhan-api';

export async function GET(request: NextRequest) {
  try {
    // In a production app, you might want to verify the user's authentication here
    // For now, we'll proceed with fetching the profile data
    
    const response = await fetchDhanProfile();
    
    if (!response.success) {
      return NextResponse.json(
        { 
          error: response.error?.errorMessage || 'Failed to fetch profile data',
          details: response.error 
        },
        { status: response.error?.errorCode === 'MISSING_CREDENTIALS' ? 500 : 400 }
      );
    }

    return NextResponse.json({
      success: true,
      data: response.data,
    });

  } catch (error) {
    console.error('Error in Dhan profile API route:', error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Handle CORS if needed
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
