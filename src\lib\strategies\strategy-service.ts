/**
 * Trading Strategy Service
 * Manages strategy execution, signal generation, and database integration
 */

import { EmaScalper, Candle, Signal } from './emaScalper';
import { ProcessedMarketData } from '@/types/dhan';
import { supabase } from '@/lib/supabase';
import { tradingLogger } from '@/lib/trading-logger';

export interface StrategyConfig {
  name: string;
  emaLength: number;
  lookbackPeriod: number;
  enabled: boolean;
}

export interface TradingSignal {
  id?: string;
  userId: string;
  signalType: 'BUY' | 'SELL';
  instrumentSymbol: string;
  instrumentName: string;
  signalPrice: number;
  strategyName: string;
  strategyParams: Record<string, any>;
  marketData?: Record<string, any>;
  candleIndex?: number;
  emaValue?: number;
  last8hHigh?: number;
  last8lLow?: number;
  isExecuted: boolean;
  executionAttempted: boolean;
  autoTradeEnabled: boolean;
  tradingMode: 'SANDBOX' | 'LIVE';
  createdAt?: string;
}

export interface MarketDataPoint {
  timestamp: Date;
  price: number;
  symbol: string;
  volume?: number;
}

export class StrategyService {
  private emaScalper: EmaScalper;
  private priceHistory: Map<string, MarketDataPoint[]> = new Map();
  private lastSignalTime: Map<string, Date> = new Map();
  private readonly MIN_SIGNAL_INTERVAL = 60000; // 1 minute minimum between signals

  constructor(config: StrategyConfig = { name: 'EMA_SCALPER', emaLength: 20, lookbackPeriod: 8, enabled: true }) {
    this.emaScalper = new EmaScalper(config.emaLength);
  }

  /**
   * Process new market data and generate signals
   */
  async processMarketData(marketData: ProcessedMarketData, userId: string): Promise<TradingSignal[]> {
    const signals: TradingSignal[] = [];
    
    try {
      // Get user's trading configuration
      const config = await this.getTradingConfig(userId);
      if (!config || !config.auto_trade_enabled) {
        return signals;
      }

      // Update price history
      this.updatePriceHistory(marketData);

      // Get price history for this instrument
      const history = this.priceHistory.get(marketData.symbol);
      if (!history || history.length < 20) {
        // Need at least 20 data points for EMA calculation
        return signals;
      }

      // Check if enough time has passed since last signal
      const lastSignal = this.lastSignalTime.get(marketData.symbol);
      if (lastSignal && Date.now() - lastSignal.getTime() < this.MIN_SIGNAL_INTERVAL) {
        return signals;
      }

      // Convert to candle format
      const candles: Candle[] = history.map(point => ({ close: point.price }));

      // Run strategy
      const strategySignals = this.emaScalper.runStrategy(candles);
      
      // Process only the latest signal
      if (strategySignals.length > 0) {
        const latestSignal = strategySignals[strategySignals.length - 1];
        
        if (latestSignal.type) {
          const tradingSignal: TradingSignal = {
            userId,
            signalType: latestSignal.type,
            instrumentSymbol: marketData.symbol,
            instrumentName: marketData.name,
            signalPrice: latestSignal.price,
            strategyName: 'EMA_SCALPER',
            strategyParams: {
              emaLength: this.emaScalper['length'],
              lookbackPeriod: 8,
              candleIndex: latestSignal.index
            },
            marketData: {
              currentPrice: marketData.currentPrice,
              change: marketData.change,
              changePercent: marketData.changePercent,
              volume: marketData.volume,
              lastUpdated: marketData.lastUpdated
            },
            candleIndex: latestSignal.index,
            emaValue: this.emaScalper['emaValues'][latestSignal.index],
            isExecuted: false,
            executionAttempted: false,
            autoTradeEnabled: config.auto_trade_enabled,
            tradingMode: config.trading_mode
          };

          // Calculate last 8 high/low
          const recentPrices = history.slice(-8).map(p => p.price);
          tradingSignal.last8hHigh = Math.max(...recentPrices);
          tradingSignal.last8lLow = Math.min(...recentPrices);

          signals.push(tradingSignal);

          // Update last signal time
          this.lastSignalTime.set(marketData.symbol, new Date());

          // Log signal generation
          tradingLogger.logSignal(userId, tradingSignal, {
            marketData: {
              currentPrice: marketData.currentPrice,
              change: marketData.change,
              volume: marketData.volume
            }
          });

          // Store signal in database
          await this.storeSignal(tradingSignal);
        }
      }

    } catch (error) {
      console.error('Error processing market data for strategy:', error);
    }

    return signals;
  }

  /**
   * Update price history for an instrument
   */
  private updatePriceHistory(marketData: ProcessedMarketData): void {
    const symbol = marketData.symbol;
    
    if (!this.priceHistory.has(symbol)) {
      this.priceHistory.set(symbol, []);
    }

    const history = this.priceHistory.get(symbol)!;
    const newPoint: MarketDataPoint = {
      timestamp: marketData.lastUpdated || new Date(),
      price: marketData.currentPrice,
      symbol: marketData.symbol,
      volume: marketData.volume
    };

    // Add new point
    history.push(newPoint);

    // Keep only last 100 points to manage memory
    if (history.length > 100) {
      history.shift();
    }

    // Remove duplicate timestamps (keep latest)
    const uniqueHistory = history.filter((point, index, arr) => {
      return index === arr.length - 1 || 
             point.timestamp.getTime() !== arr[index + 1].timestamp.getTime();
    });

    this.priceHistory.set(symbol, uniqueHistory);
  }

  /**
   * Get user's trading configuration
   */
  private async getTradingConfig(userId: string) {
    const { data, error } = await supabase
      .from('trading_configuration')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error) {
      console.error('Error fetching trading config:', error);
      return null;
    }

    return data;
  }

  /**
   * Store trading signal in database
   */
  private async storeSignal(signal: TradingSignal): Promise<string | null> {
    try {
      const { data, error } = await supabase
        .from('trading_signals')
        .insert({
          user_id: signal.userId,
          signal_type: signal.signalType,
          instrument_symbol: signal.instrumentSymbol,
          instrument_name: signal.instrumentName,
          signal_price: signal.signalPrice,
          strategy_name: signal.strategyName,
          strategy_params: signal.strategyParams,
          market_data: signal.marketData,
          candle_index: signal.candleIndex,
          ema_value: signal.emaValue,
          last_8h_high: signal.last8hHigh,
          last_8l_low: signal.last8lLow,
          is_executed: signal.isExecuted,
          execution_attempted: signal.executionAttempted,
          auto_trade_enabled: signal.autoTradeEnabled,
          trading_mode: signal.tradingMode
        })
        .select('id')
        .single();

      if (error) {
        console.error('Error storing signal:', error);
        return null;
      }

      // Update strategy performance
      await this.updateStrategyPerformance(signal.userId, signal);

      return data.id;
    } catch (error) {
      console.error('Error in storeSignal:', error);
      return null;
    }
  }

  /**
   * Update strategy performance metrics
   */
  private async updateStrategyPerformance(userId: string, signal: TradingSignal): Promise<void> {
    try {
      const { error } = await supabase.rpc('update_strategy_performance', {
        p_user_id: userId,
        p_strategy_name: signal.strategyName,
        p_instrument_symbol: signal.instrumentSymbol,
        p_signal_type: signal.signalType,
        p_trading_mode: signal.tradingMode
      });

      if (error) {
        console.error('Error updating strategy performance:', error);
      }
    } catch (error) {
      console.error('Error in updateStrategyPerformance:', error);
    }
  }

  /**
   * Get recent signals for a user
   */
  async getRecentSignals(userId: string, limit: number = 50): Promise<TradingSignal[]> {
    const { data, error } = await supabase
      .from('trading_signals')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching recent signals:', error);
      return [];
    }

    return data.map(this.mapDbSignalToTradingSignal);
  }

  /**
   * Map database signal to TradingSignal interface
   */
  private mapDbSignalToTradingSignal(dbSignal: any): TradingSignal {
    return {
      id: dbSignal.id,
      userId: dbSignal.user_id,
      signalType: dbSignal.signal_type,
      instrumentSymbol: dbSignal.instrument_symbol,
      instrumentName: dbSignal.instrument_name,
      signalPrice: parseFloat(dbSignal.signal_price),
      strategyName: dbSignal.strategy_name,
      strategyParams: dbSignal.strategy_params,
      marketData: dbSignal.market_data,
      candleIndex: dbSignal.candle_index,
      emaValue: dbSignal.ema_value ? parseFloat(dbSignal.ema_value) : undefined,
      last8hHigh: dbSignal.last_8h_high ? parseFloat(dbSignal.last_8h_high) : undefined,
      last8lLow: dbSignal.last_8l_low ? parseFloat(dbSignal.last_8l_low) : undefined,
      isExecuted: dbSignal.is_executed,
      executionAttempted: dbSignal.execution_attempted,
      autoTradeEnabled: dbSignal.auto_trade_enabled,
      tradingMode: dbSignal.trading_mode,
      createdAt: dbSignal.created_at
    };
  }
}

// Export singleton instance
export const strategyService = new StrategyService();
