/**
 * Market Status Indicator Component
 * Shows current market status, data source, and countdown timers
 */

import React, { useState, useEffect } from 'react';
import { Clock, Wifi, WifiOff, Database, Activity, AlertCircle } from 'lucide-react';
import { MarketDataStatus, DataSource } from '@/hooks/use-market-data';
import { formatTimeUntil, getMarketStatusText } from '@/lib/market-hours';

interface MarketStatusIndicatorProps {
  status: MarketDataStatus;
  className?: string;
}

export function MarketStatusIndicator({ status, className = '' }: MarketStatusIndicatorProps) {
  const [currentTime, setCurrentTime] = useState(new Date());

  // Update current time every second for countdown
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const getDataSourceInfo = (dataSource: DataSource) => {
    switch (dataSource) {
      case 'WEBSOCKET':
        return {
          icon: <Wifi className="w-4 h-4" />,
          label: 'Live WebSocket',
          color: 'text-green-600',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200'
        };
      case 'LIVE':
        return {
          icon: <Activity className="w-4 h-4" />,
          label: 'Live API',
          color: 'text-blue-600',
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200'
        };
      case 'HISTORICAL':
        return {
          icon: <Database className="w-4 h-4" />,
          label: 'Historical Data',
          color: 'text-orange-600',
          bgColor: 'bg-orange-50',
          borderColor: 'border-orange-200'
        };
      case 'DEMO':
        return {
          icon: <AlertCircle className="w-4 h-4" />,
          label: 'Demo Data',
          color: 'text-gray-600',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200'
        };
    }
  };

  const getMarketStatusColor = (isOpen: boolean) => {
    return isOpen ? 'text-green-600' : 'text-red-600';
  };

  const dataSourceInfo = getDataSourceInfo(status.dataSource);
  const { nse, mcx } = status.marketStatus;

  // Calculate time until next market event
  const getNextEventInfo = () => {
    if (nse.isOpen || mcx.isOpen) {
      // Market is open, show time until close
      const nextClose = nse.isOpen ? nse.nextCloseTime : mcx.nextCloseTime;
      if (nextClose) {
        const timeUntilClose = nextClose.getTime() - currentTime.getTime();
        return {
          label: 'Closes in',
          time: formatTimeUntil(timeUntilClose),
          isPositive: false
        };
      }
    } else {
      // Market is closed, show time until open
      const nextOpen = nse.nextOpenTime || mcx.nextOpenTime;
      if (nextOpen) {
        const timeUntilOpen = nextOpen.getTime() - currentTime.getTime();
        return {
          label: 'Opens in',
          time: formatTimeUntil(timeUntilOpen),
          isPositive: true
        };
      }
    }
    return null;
  };

  const nextEventInfo = getNextEventInfo();

  return (
    <div className={`bg-white rounded-lg border shadow-sm p-4 ${className}`}>
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-medium text-gray-900">Market Status</h3>
        <div className={`flex items-center gap-2 px-2 py-1 rounded-full border ${dataSourceInfo.bgColor} ${dataSourceInfo.borderColor}`}>
          {dataSourceInfo.icon}
          <span className={`text-xs font-medium ${dataSourceInfo.color}`}>
            {dataSourceInfo.label}
          </span>
        </div>
      </div>

      <div className="space-y-3">
        {/* Market Status */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="text-sm text-gray-600">NSE:</div>
            <div className={`text-sm font-medium ${getMarketStatusColor(nse.isOpen)}`}>
              {getMarketStatusText(nse)}
            </div>
          </div>
          <div className="flex items-center gap-2">
            <div className="text-sm text-gray-600">MCX:</div>
            <div className={`text-sm font-medium ${getMarketStatusColor(mcx.isOpen)}`}>
              {getMarketStatusText(mcx)}
            </div>
          </div>
        </div>

        {/* Countdown Timer */}
        {nextEventInfo && (
          <div className="flex items-center justify-center gap-2 p-2 bg-gray-50 rounded-md">
            <Clock className="w-4 h-4 text-gray-500" />
            <span className="text-sm text-gray-600">{nextEventInfo.label}:</span>
            <span className={`text-sm font-mono font-medium ${
              nextEventInfo.isPositive ? 'text-green-600' : 'text-orange-600'
            }`}>
              {nextEventInfo.time}
            </span>
          </div>
        )}

        {/* Data Freshness */}
        {status.lastUpdate && (
          <div className="flex items-center justify-between text-xs text-gray-500">
            <span>Last updated:</span>
            <span>{status.lastUpdate.toLocaleTimeString()}</span>
          </div>
        )}

        {/* WebSocket Status */}
        {status.websocketStatus && (
          <div className="flex items-center justify-between text-xs">
            <span className="text-gray-500">WebSocket:</span>
            <div className="flex items-center gap-1">
              {status.websocketStatus.isConnected ? (
                <>
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-green-600">Connected</span>
                </>
              ) : status.websocketStatus.isConnecting ? (
                <>
                  <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>
                  <span className="text-yellow-600">Connecting...</span>
                </>
              ) : (
                <>
                  <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                  <span className="text-red-600">Disconnected</span>
                </>
              )}
            </div>
          </div>
        )}

        {/* Cache Info */}
        {status.cacheInfo?.hasCache && (
          <div className="flex items-center justify-between text-xs text-gray-500">
            <span>Cached data:</span>
            <span>
              {status.cacheInfo.isFromToday ? 'Today' : `${Math.round(status.cacheInfo.ageInHours)}h ago`}
            </span>
          </div>
        )}
      </div>
    </div>
  );
}

export default MarketStatusIndicator;
