/**
 * Custom hook for fetching and managing Dhan profile data
 * Implements intelligent caching to prevent rate limiting
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { UseDhanProfileState, ProcessedProfileData } from '@/types/dhan';
import { profileCache } from '@/lib/profile-cache';

interface DhanProfileApiResponse {
  success: boolean;
  data?: ProcessedProfileData;
  error?: string;
  details?: any;
}

export function useDhanProfile(): UseDhanProfileState {
  const [data, setData] = useState<ProcessedProfileData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const mountedRef = useRef(true);

  const fetchProfile = useCallback(async (forceRefresh: boolean = false) => {
    try {
      setLoading(true);
      setError(null);

      // Use cache service instead of direct API call
      const profileData = await profileCache.getProfileData(forceRefresh);

      if (!mountedRef.current) return;

      if (profileData) {
        setData(profileData);
        setError(null);
      } else {
        // No data available (neither cached nor fresh)
        setError('Profile data is currently unavailable. Please try again later.');
      }

    } catch (err) {
      if (!mountedRef.current) return;

      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Error fetching Dhan profile:', err);

      // Try to get cached data as fallback
      try {
        const cachedData = await profileCache.getProfileData(false);
        if (cachedData) {
          setData(cachedData);
          setError('Using cached profile data. ' + errorMessage);
        }
      } catch (cacheError) {
        console.error('Failed to get cached profile data:', cacheError);
      }
    } finally {
      if (mountedRef.current) {
        setLoading(false);
      }
    }
  }, []);

  const refetch = useCallback(async () => {
    console.log('Manual profile refresh requested');
    await fetchProfile(true); // Force refresh when manually requested
  }, [fetchProfile]);

  // Cleanup on unmount
  useEffect(() => {
    mountedRef.current = true;
    return () => {
      mountedRef.current = false;
    };
  }, []);

  // Initial fetch on mount
  useEffect(() => {
    fetchProfile(false); // Use cached data if available
  }, [fetchProfile]);

  return {
    data,
    loading,
    error,
    refetch,
  };
}
